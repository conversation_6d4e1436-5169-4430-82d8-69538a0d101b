<template>
  <div class="con custom-template">
    <div class="navLlist">
      <div class="top-bar">
        <el-form inline :model="store[store.initType].getListApiParams">
          <el-form-item
            label="起止时间"
            class="date-picker"
            v-if="store.initType == 'historyData'"
          >
            <n-date-picker
              v-model:value="store[store.initType].getListApiParams.date"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :is-date-disabled="disabledDate"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-form>
        <el-button
          type="primary"
          @click="onSearchBtnClick"
          v-if="store.initType == 'historyData'"
          :loading="store.isLoading"
          >查询</el-button
        >

        <div class="btns">
          <el-button
            type="primary"
            v-if="
              store.initType === 'historyData' &&
              store.historyData.currentSelected === 'rateBlocking'
            "
            @click="onSetDataClick"
            :loading="store.isLoading"
            >设置数据</el-button
          >
          <el-button
            type="primary"
            v-if="store.initType === 'historyData'"
            @click="showThresholdDialog"
            :loading="store.isLoading"
            >设置门限</el-button
          >
        </div>
      </div>

      <el-button-group class="btn-group">
        <el-button
          :color="themeStore.themeColor"
          :plain="store.initType == 'historyData' ? false : true"
          @click="() => store.onChildTabClick('historyData')"
          >历史数据</el-button
        >
        <el-button
          :color="themeStore.themeColor"
          :plain="store.initType != 'historyData' ? false : true"
          @click="() => store.onChildTabClick('realTimeData')"
          >实时数据</el-button
        >
      </el-button-group>
    </div>

    <div class="content" v-loading="store.isLoading">
      <historyData
        ref="historyDataRef"
        v-if="store.initType == 'historyData'"
      />
      <realTimeData ref="realTimeDataRef" v-else />
    </div>

    <!-- 门限设置弹窗 -->
    <threshold-dialog
      v-model:visible="thresholdDialogVisible"
      @success="handleThresholdSuccess"
    />

    <!-- 数据设置弹窗 -->
    <data-setting-dialog
      v-model:visible="dataSettingDialogVisible"
      @success="handleDataSettingSuccess"
    />
  </div>
</template>

<script setup>
import { onMounted, ref, onUnmounted } from "vue";
import { NDatePicker } from "naive-ui";
import { useThemeStore } from "@/store/modules/theme";
import { useSynthesizeInfo } from "@/store/modules/data-analysis/synthesize";
import historyData from "./historyData.vue";
import realTimeData from "./realTimeData.vue";
import ThresholdDialog from "./components/ThresholdDialog.vue";
import DataSettingDialog from "./components/DataSettingDialog.vue";
import { unsubscribeAll } from "@/api/ws";
const themeStore = useThemeStore();
const store = useSynthesizeInfo();
const historyDataRef = ref();
const realTimeDataRef = ref();
const thresholdDialogVisible = ref(false);
const dataSettingDialogVisible = ref(false);

const onSearchBtnClick = () => {
  store.search(store.initType);
};

const onSetDataClick = () => {
  dataSettingDialogVisible.value = true;
};

const showThresholdDialog = () => {
  thresholdDialogVisible.value = true;
};

const handleThresholdSuccess = () => {
  // store.search(store.initType);
};

const handleDataSettingSuccess = () => {
  // store.search(store.initType);
};

const disabledDate = (time) => {
  const currentDate = new Date();
  return time.getTime() > currentDate.getTime();
};

onMounted(async () => {
  await store.init();
  if (historyDataRef.value) {
    historyDataRef.value.initData?.();
  }
});
onUnmounted(() => {
  unsubscribeAll();
});
</script>

<style lang="scss" scoped>
.con {
  .top-bar {
    display: flex;
    background-color: #fff;
    align-items: center;
    height: 56px;
    position: relative;
    flex: 1;
    .form-item {
      width: 150px;
    }
    :deep(.el-form-item) {
      margin: 12px;
    }
    .btns {
      margin-left: auto;
      display: flex;
      gap: 12px;
    }
  }
  .navLlist {
    display: flex;
    background-color: #fff;
    align-items: center;
    justify-content: space-between;
    .btn-group {
      width: 200px;
      margin-left: 16px;
    }
  }
}
</style>
