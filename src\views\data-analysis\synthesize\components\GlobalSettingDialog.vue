<template>
  <el-dialog
    v-model="dialogVisible"
    title="全局设置"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="global-setting">
      <!-- 顶部工具栏 -->
      <div class="toolbar">
        <el-form :inline="true" :model="queryParams">
          <el-form-item label="时间范围">
            <n-date-picker
              v-model:value="dateRange"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :is-date-disabled="(timestamp) => timestamp > Date.now()"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" :loading="loading">
              查询
            </el-button>
          </el-form-item>
        </el-form>
        <div class="right-buttons">
          <el-button type="primary" @click="handleAdd">新增规则</el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        height="500"
      >
        <el-table-column prop="startTimeSeconds" label="开始时间">
          <template #default="{ row }">
            {{
              dayjs(row.startTimeSeconds * 1000).format("YYYY-MM-DD HH:mm:ss")
            }}
          </template>
        </el-table-column>
        <el-table-column prop="endTimeSeconds" label="结束时间">
          <template #default="{ row }">
            {{ dayjs(row.endTimeSeconds * 1000).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column prop="transmitter" label="发波台">
          <template #default="{ row }">
            {{ proxy.configure.typeObj[row.transmitter] }}
          </template>
        </el-table-column>
        <el-table-column
          prop="message"
          label="备注信息"
          show-overflow-tooltip
        />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button link type="danger" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="ruleDialog.visible"
      :title="ruleDialog.type === 'add' ? '新增规则' : '编辑规则'"
      width="500px"
      append-to-body
    >
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="开始时间" prop="startTimeSeconds">
          <n-date-picker
            v-model:value="ruleForm.startTimeSeconds"
            type="datetime"
            placeholder="请选择开始时间"
            :is-date-disabled="(timestamp) => timestamp > Date.now()"
            clearable
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTimeSeconds">
          <n-date-picker
            v-model:value="ruleForm.endTimeSeconds"
            type="datetime"
            placeholder="请选择结束时间"
            :is-date-disabled="(timestamp) => timestamp > Date.now()"
            clearable
          />
        </el-form-item>
        <!-- <el-form-item label="发波台" prop="transmitter">
          <el-select
            v-model="ruleForm.transmitter"
            placeholder="请选择"
            class="select"
            @change="getsetAlarmSettings"
          >
            <el-option
              v-for="item in transmitterIdList"
              :key="item.transmitterId"
              :label="item.transmitterName"
              :value="item.transmitterId"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="备注信息" prop="message">
          <el-input
            v-model="ruleForm.message"
            type="textarea"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ruleDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleRuleSubmit"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
import apiAjax from "@/api/index";
const { proxy } = getCurrentInstance();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:visible", "success"]);

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

// 查询参数
const queryParams = reactive({
  page: 1,
  size: 10,
  startTime: undefined,
  endTime: undefined,
});

// 日期范围 - 默认设置为7天前到今天
const dateRange = ref([]);
// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    // NaiveUI返回的是毫秒时间戳，需要转换为秒时间戳
    queryParams.startTime = Math.floor(newVal[0] / 1000);
    queryParams.endTime = Math.floor(newVal[1] / 1000);
  } else {
    // 如果没有选择时间，设置默认的7天前到今天
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    sevenDaysAgo.setHours(0, 0, 0, 0);

    queryParams.startTime = Math.floor(sevenDaysAgo.getTime() / 1000);
    queryParams.endTime = Math.floor(today.getTime() / 1000);
  }
});

// 表格数据
const loading = ref(false);
const tableData = ref([]);
const total = ref(0);
const selectedRows = ref([]);

// 规则表单
const ruleDialog = reactive({
  visible: false,
  type: "add", // add or edit
});

const ruleFormRef = ref();
const ruleForm = reactive({
  id: undefined,
  startTimeSeconds: undefined,
  endTimeSeconds: undefined,
  ignoreFlag: 0,
  message: "",
  // transmitter: "",
  timeSeconds: Math.floor(Date.now() / 1000),
  timestamp: new Date().toISOString(),
});

const rules = {
  startTimeSeconds: [
    { required: true, message: "请选择开始时间", trigger: "change" },
  ],
  endTimeSeconds: [
    { required: true, message: "请选择结束时间", trigger: "change" },
    {
      validator: (rule, value, callback) => {
        if (value <= ruleForm.startTimeSeconds) {
          callback(new Error("结束时间必须大于开始时间"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  // transmitter: [{ required: true, message: "请选择发波台", trigger: "blur" }],
  ignoreFlag: [{ required: true, message: "请选择状态", trigger: "change" }],
};

// 查询数据
const handleQuery = async () => {
  loading.value = true;
  try {
    const res = await apiAjax.post(
      `/api/jnx/dataProcess/getComprehensivePlan?endTime=${queryParams.endTime}&page=${queryParams.page}&size=${queryParams.size}&startTime=${queryParams.startTime}`
    );

    if (res && res[0]) {
      tableData.value = res[0].content;
      total.value = res[0].totalElements;
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

// 分页大小变化
const handleSizeChange = (val) => {
  queryParams.size = val;
  queryParams.page = 1;
  handleQuery();
};

// 页码变化
const handleCurrentChange = (val) => {
  queryParams.page = val;
  handleQuery();
};

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 新增规则
const handleAdd = () => {
  ruleDialog.type = "add";
  ruleDialog.visible = true;
  // 重置表单数据，新增时不需要id
  Object.assign(ruleForm, {
    id: undefined,
    startTimeSeconds: undefined,
    endTimeSeconds: undefined,
    ignoreFlag: 0,
    message: "",
    // transmitter: "",
    timeSeconds: Math.floor(Date.now() / 1000),
    timestamp: new Date().toISOString(),
  });
};

// 编辑规则
const handleEdit = (row) => {
  ruleDialog.type = "edit";
  ruleDialog.visible = true;
  // 编辑时需要保留id
  Object.assign(ruleForm, {
    ...row,
    timeSeconds: Math.floor(Date.now() / 1000),
    timestamp: new Date().toISOString(),
  });
};

// 删除规则
const handleDelete = (row) => {
  ElMessageBox.confirm("确定要删除该规则吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await apiAjax.post(`/api/jnx/dataProcess/deleteComprehensivePlan`, [
        { id: row.id },
      ]);
      ElMessage.success("删除成功");
      handleQuery();
    } catch (error) {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  });
};

// 提交规则
const handleRuleSubmit = async () => {
  if (!ruleFormRef.value) return;

  await ruleFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 更新时间戳
        const submitData = {
          ...ruleForm,
          timeSeconds: Math.floor(Date.now() / 1000),
          timestamp: new Date().toISOString(),
        };

        // 如果是新增，删除id字段
        if (ruleDialog.type === "add") {
          delete submitData.id;
        }

        let datas = await apiAjax.post(
          "/api/jnx/dataProcess/setComprehensivePlan",
          [submitData]
        );
        if (datas.code !== 500) {
          ElMessage.success(
            `${ruleDialog.type === "add" ? "新增" : "编辑"}成功`
          );
          ruleDialog.visible = false;
          handleQuery();
        } else {
          ElMessage.error(datas.message);
        }
      } catch (error) {
        console.error(
          `${ruleDialog.type === "add" ? "新增" : "编辑"}失败:`,
          error
        );
        ElMessage.error(`${ruleDialog.type === "add" ? "新增" : "编辑"}失败`);
      }
    }
  });
};

// 发波台
const transmitterIdList = Object.keys(proxy.configure.typeObj).map((key) => {
  return {
    transmitterId: key,
    transmitterName: proxy.configure.typeObj[key],
  };
});

// 监听弹窗显示
watch(
  () => dialogVisible.value,
  (newVal) => {
    if (newVal) {
      // 设置默认时间范围为7天前到今天
      const today = new Date();
      today.setHours(23, 59, 59, 999);
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      sevenDaysAgo.setHours(0, 0, 0, 0);

      // 先设置查询参数
      queryParams.startTime = Math.floor(sevenDaysAgo.getTime() / 1000);
      queryParams.endTime = Math.floor(today.getTime() / 1000);

      // 设置日期范围，NaiveUI需要毫秒时间戳
      dateRange.value = [queryParams.startTime * 1000, queryParams.endTime * 1000];

      // 直接查询一次
      handleQuery();
    }
  }
);

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  ruleDialog.visible = false;
  selectedRows.value = [];
  queryParams.page = 1;
};

defineExpose({
  handleClose,
});
</script>

<style lang="scss" scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.global-setting {
  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
